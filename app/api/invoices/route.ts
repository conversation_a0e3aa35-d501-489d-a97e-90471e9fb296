import { NextResponse, type NextRequest } from "next/server";
import { getDb, storeInvoicePdf } from "@/lib/blob-storage";
import {
  extractInvoiceIssuer,
  extractInvoiceDate,
  extractInvoiceItems,
  extractInvoiceTotal,
} from "@/lib/openai";
import { extractTextFromPdf } from "@/lib/pdf";
import { parse, startOfMonth, format } from "date-fns";

const MY_VAT_ID = process.env.MY_VAT_ID;
if (!MY_VAT_ID) {
  throw new Error("MY_VAT_ID environment variable is required");
}

export const dynamic = "force-dynamic";

export const GET = async (request: NextRequest) => {
  // Get month from query params or use current month
  const searchParams = request.nextUrl.searchParams;
  const monthParam = searchParams.get("month"); // Format: "2024-03"

  let targetDate: Date;

  if (monthParam) {
    targetDate = parse(monthParam, "yyyy-MM", new Date());
  } else {
    targetDate = new Date();
  }

  console.log("📅 Loading invoices for:", monthParam || "current month");

  const db = await getDb(targetDate);
  const invoices = db.data.invoices;

  return NextResponse.json(invoices);
};

export const POST = async (request: Request) => {
  console.log("📝 Processing new invoice upload request...");
  const formData = await request.formData();
  const files = formData.getAll("files") as File[];

  if (!files.length) {
    console.log("❌ No files received");
    return NextResponse.json({ error: "No files received" }, { status: 400 });
  }

  const processFile = async (file: File) => {
    if (!file.type.includes("pdf")) {
      console.log(`❌ Invalid file type for ${file.name}`);
      return { error: { name: file.name, error: "Invalid file type" } };
    }

    try {
      console.log(`🔄 Processing ${file.name}...`);
      const buffer = Buffer.from(await file.arrayBuffer());
      const fileData = `data:application/pdf;base64,${buffer.toString("base64")}`;

      // Extract text from PDF for VAT checking and storage
      console.log(`📄 Extracting text from ${file.name}...`);
      const text = await extractTextFromPdf(buffer);

      if (!text) {
        console.log(`❌ Failed to extract text from ${file.name}`);
        return {
          error: { name: file.name, error: "Failed to extract text from PDF" },
        };
      }

      // Check for VAT ID presence with more flexible matching
      const hasMyVAT = text.replace(/\s+/g, "").includes(MY_VAT_ID);
      console.log(
        `🔍 VAT ID check for ${file.name}: ${hasMyVAT ? "Found" : "Not found"}`
      );

      // Get issuer, date, items and total from OpenAI using PDF directly
      console.log(`🤖 Analyzing ${file.name} with OpenAI...`);
      const [issuer, extractedDate, items, { amount, currency }] =
        await Promise.all([
          extractInvoiceIssuer(fileData),
          extractInvoiceDate(fileData),
          extractInvoiceItems(fileData),
          extractInvoiceTotal(fileData),
        ]);

      console.log(`Extracted info for ${file.name}:
        Issuer: ${issuer}
        Date: ${extractedDate}
        Items: ${items}
        Total: ${amount} ${currency}
      `);

      // Use the extracted date or fallback to current date
      const invoiceDate = extractedDate ? new Date(extractedDate) : new Date();
      const id = `${Date.now()}-${file.name}`;
      const monthStr = format(startOfMonth(invoiceDate), 'yyyy-MM');

      // Store PDF in blob storage
      const pdfUrl = await storeInvoicePdf(id, monthStr, buffer);

      return {
        success: {
          id,
          name: file.name,
          pdfUrl, // Vercel Blob Storage URL for the PDF
          timestamp: Date.now(),
          issuer,
          hasMyVAT,
          extractedText: text,
          issueDate: extractedDate,
          items,
          totalAmount: amount,
          currency,
          month: startOfMonth(invoiceDate),
          monthString: monthStr, // Add formatted month string for easier frontend use
        },
      };
    } catch (error) {
      console.error(`💥 Error processing ${file.name}:`, error);
      return { error: { name: file.name, error: "Failed to process invoice" } };
    }
  };

  // Process all files in parallel
  console.log("🚀 Starting parallel processing of", files.length, "files...");
  const results = await Promise.all(files.map(processFile));

  // Separate successes and errors
  const successes = results
    .filter((r): r is { success: any } => "success" in r)
    .map((r) => r.success);
  const errors = results
    .filter((r): r is { error: any } => "error" in r)
    .map((r) => r.error);

  // Group successes by month for batch database updates
  const byMonth: Record<string, typeof successes> = successes.reduce(
    (acc, invoice) => {
      const month = invoice.month;
      if (!acc[month.toISOString()]) {
        acc[month.toISOString()] = [];
      }
      acc[month.toISOString()].push(invoice);
      return acc;
    },
    {} as Record<string, typeof successes>
  );

  // Batch update database for each month
  console.log("💾 Batch updating database...");
  await Promise.all(
    Object.entries(byMonth).map(async ([monthStr, invoices]) => {
      const month = new Date(monthStr);
      const db = await getDb(month);
      if (!db.data.invoices) {
        db.data.invoices = [];
      }
      db.data.invoices.push(...invoices);
      await db.write();
      console.log(
        `✅ Updated database for ${format(month, "yyyy-MM")} with ${
          invoices.length
        } invoices`
      );
    })
  );

  // Get unique months that were affected for cache invalidation
  // Convert from ISO date strings to yyyy-MM format for React Query cache keys
  const affectedMonths = Object.keys(byMonth).map(isoDateString => {
    const date = new Date(isoDateString);
    return format(date, 'yyyy-MM');
  });

  return NextResponse.json({
    results: successes.map(({ month, ...rest }) => rest),
    errors,
    success: successes.length > 0,
    affectedMonths, // Include affected months for frontend cache invalidation
  });
};

export async function DELETE(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const monthParam = searchParams.get("month");

  try {
    const targetDate = monthParam
      ? parse(monthParam, "yyyy-MM", new Date())
      : new Date();

    const db = await getDb(targetDate);
    db.data.invoices = [];
    db.data.transactionToInvoice = {};
    await db.write();
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to delete invoices:", error);
    return NextResponse.json(
      { error: "Failed to delete invoices" },
      { status: 500 }
    );
  }
}
