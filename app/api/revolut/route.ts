import { NextRequest, NextResponse } from "next/server";
import { parse, format, startOfMonth, endOfMonth } from "date-fns";
import { getDb } from "@/lib/blob-storage";

const REVOLUT_API_URL = "https://business.revolut.com/api/1.0";

async function fetchRevolutTransactions(month: string, token?: string) {
  console.log("🔄 Fetching Revolut transactions for month:", month);

  const date = parse(month, "yyyy-MM", new Date());
  const fromDate = format(startOfMonth(date), "yyyy-MM-dd");
  const toDate = format(endOfMonth(date), "yyyy-MM-dd");

  // Use provided token or fallback to environment variable
  const authToken = token || process.env.REVOLUT_API_TOKEN;

  console.log("🔑 Using token:", authToken ? `${authToken.substring(0, 10)}...` : "none");
  console.log("📅 Date range:", fromDate, "to", toDate);

  const response = await fetch(
    `${REVOLUT_API_URL}/transactions?from=${fromDate}&to=${toDate}`,
    {
      headers: {
        "Authorization": `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    }
  );

  console.log("📡 Response status:", response.status, response.statusText);

  if (!response.ok) {
    const errorText = await response.text();
    console.log("❌ Error response:", errorText);
    throw new Error(`Failed to fetch Revolut transactions: ${response.statusText} - ${errorText}`);
  }

  return response.json();
}

export async function GET(request: NextRequest) {
  try {
    const month = request.nextUrl.searchParams.get("month");
    const token = request.nextUrl.searchParams.get("token"); // Optional OAuth token

    if (!month) {
      return NextResponse.json({ error: "Month parameter is required" }, { status: 400 });
    }

    // Check if we have either a provided token or environment token
    if (!token && !process.env.REVOLUT_API_TOKEN) {
      console.error("❌ Missing REVOLUT_API_TOKEN environment variable and no token provided");
      return NextResponse.json(
        { error: "Revolut API token not configured" },
        { status: 500 }
      );
    }

    const transactions = await fetchRevolutTransactions(month, token || undefined);

    // Store the transactions in our database
    const { data, write } = await getDb(parse(month, "yyyy-MM", new Date()));
    data.transactions = transactions;
    await write();

    return NextResponse.json({ success: true, transactions });
  } catch (error) {
    console.error("❌ Error fetching Revolut transactions:", error);
    return NextResponse.json(
      { error: "Failed to fetch Revolut transactions" },
      { status: 500 }
    );
  }
}

//// https://revoltage-acc-emailer.vercel.app/api/auth/callback/revolut
