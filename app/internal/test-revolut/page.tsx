"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

export default function TestRevolutPage() {
  const [authUrl, setAuthUrl] = useState("");
  const [authCode, setAuthCode] = useState("");
  const [accessToken, setAccessToken] = useState("");
  const [month, setMonth] = useState("2025-01");
  const [transactions, setTransactions] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // Step 1: Generate Revolut OAuth URL
  const generateAuthUrl = () => {
    const clientId = process.env.NEXT_PUBLIC_REVOLUT_CLIENT_ID || "vO_rNFnDZQ3Co3QJgc5Jl5EgQfxsjANXIJpn8URlQCk";
    const redirectUri = "https://revoltage-acc-emailer.vercel.app/api/auth/callback/revolut";
    const scope = "READ";
    const state = "test-state-" + Date.now();
    
    const url = `https://oba-auth.revolut.com/auth?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${scope}&state=${state}&response_type=code`;
    setAuthUrl(url);
  };

  // Step 2: Exchange auth code for access token
  const exchangeCodeForToken = async () => {
    if (!authCode) {
      setError("Please enter the authorization code");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const response = await fetch(`/api/auth/callback/revolut?code=${authCode}`);
      const data = await response.json();
      
      if (response.ok && data.access_token) {
        setAccessToken(data.access_token);
        setError("");
      } else {
        setError(`Token exchange failed: ${data.error || "Unknown error"}`);
      }
    } catch (err) {
      setError(`Network error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Step 3: Test fetching transactions with the token
  const testTransactions = async () => {
    if (!accessToken) {
      setError("Please get an access token first");
      return;
    }

    setLoading(true);
    setError("");

    try {
      // Test direct API call with the token
      const response = await fetch(`/api/revolut?month=${month}&token=${accessToken}`);
      const data = await response.json();
      
      if (response.ok) {
        setTransactions(data);
        setError("");
      } else {
        setError(`API call failed: ${data.error || "Unknown error"}`);
      }
    } catch (err) {
      setError(`Network error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Test current hardcoded token approach
  const testCurrentImplementation = async () => {
    setLoading(true);
    setError("");

    try {
      const response = await fetch(`/api/revolut?month=${month}`);
      const data = await response.json();
      
      if (response.ok) {
        setTransactions(data);
        setError("");
      } else {
        setError(`Current implementation failed: ${data.error || "Unknown error"}`);
      }
    } catch (err) {
      setError(`Network error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Revolut Integration Test</h1>
        <p className="text-muted-foreground">Test the Revolut OAuth flow and API integration</p>
      </div>

      {error && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <p className="text-red-600">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Step 1: OAuth Authorization */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Step 1: OAuth Authorization</CardTitle>
          <CardDescription>
            Generate the Revolut OAuth URL and authorize the application
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={generateAuthUrl} variant="outline">
            Generate OAuth URL
          </Button>
          
          {authUrl && (
            <div className="space-y-2">
              <Label>Authorization URL:</Label>
              <Textarea 
                value={authUrl} 
                readOnly 
                className="h-20 text-xs"
              />
              <Button 
                onClick={() => window.open(authUrl, '_blank')}
                className="w-full"
              >
                Open in New Tab → Authorize App
              </Button>
              <p className="text-sm text-muted-foreground">
                After authorization, copy the 'code' parameter from the callback URL
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Step 2: Token Exchange */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Step 2: Exchange Code for Token</CardTitle>
          <CardDescription>
            Enter the authorization code to get an access token
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="authCode">Authorization Code</Label>
            <Input
              id="authCode"
              value={authCode}
              onChange={(e) => setAuthCode(e.target.value)}
              placeholder="Enter the code from the callback URL"
            />
          </div>
          
          <Button 
            onClick={exchangeCodeForToken} 
            disabled={loading || !authCode}
          >
            {loading ? "Exchanging..." : "Get Access Token"}
          </Button>
          
          {accessToken && (
            <div className="space-y-2">
              <Label>Access Token:</Label>
              <Badge variant="secondary" className="break-all">
                {accessToken.substring(0, 20)}...
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      <Separator className="my-6" />

      {/* Step 3: Test API Calls */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Step 3: Test API Calls</CardTitle>
          <CardDescription>
            Test fetching transactions with different approaches
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="month">Month (YYYY-MM)</Label>
            <Input
              id="month"
              value={month}
              onChange={(e) => setMonth(e.target.value)}
              placeholder="2025-01"
            />
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button 
              onClick={testTransactions} 
              disabled={loading || !accessToken}
              variant="default"
            >
              {loading ? "Testing..." : "Test with OAuth Token"}
            </Button>
            
            <Button 
              onClick={testCurrentImplementation} 
              disabled={loading}
              variant="outline"
            >
              {loading ? "Testing..." : "Test Current Implementation"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {transactions && (
        <Card>
          <CardHeader>
            <CardTitle>API Response</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">
              {JSON.stringify(transactions, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
